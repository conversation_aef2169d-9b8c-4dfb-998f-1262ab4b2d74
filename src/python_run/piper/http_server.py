#!/usr/bin/env python3
import io
import wave
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from enum import Enum

from fastapi import FastAPI, Form
from fastapi.responses import JSONResponse, StreamingResponse

from . import PiperVoice
from .download import ensure_voice_exists, find_voice, get_voices

logging.basicConfig(level=logging.INFO)
_LOGGER = logging.getLogger(__name__)

app = FastAPI()

# Gender enum for dropdown
class Gender(str, Enum):
    male = "male"
    female = "female"
    finetuned_female = "finetuned-female"
    finetune_girl_2 = "finetune_girl_2"
    en_male_ryan = "en-male-ryan"

# Model configurations
MODEL_CONFIGS = {
    "male": {
        "model": "/app/models/ne_NP-chitwan-medium.onnx",
        "config": "/app/config/chitwan.json"
    },
    "female": {
        "model": "/app/models/ne_NP-google-medium.onnx",
        "config": "/app/config/google.json"
    },
    "finetuned-female": {
        "model": "/app/models/finetuned_nepali_girl.onnx",
        "config": "/app/config/finetuned_girl.json"
    },
    "finetune_girl_2": {
        "model": "/app/models/nepali-finetuned_girl_2.onnx",
        "config": "/app/config/google-finetuned.json"
    },
    "en-male-ryan": {
        "model": "/app/models/en_US-ryan-high.onnx",
        "config": "/app/config/en-ryan-high.json"
    }
}

# Global model cache
loaded_voice: Optional[PiperVoice] = None
current_gender: Optional[str] = None
synthesize_args: Dict[str, Any] = {}

def load_model_for_gender(
    gender: str,
    cuda: bool = False,
    data_dir: str = str(Path.cwd()),
    download_dir: Optional[str] = None,
    update_voices: bool = False,
    speaker: int = 0,
    length_scale: float = 1.0,
    noise_scale: float = 0.667,
    noise_w: float = 0.8,
    sentence_silence: float = 0.0
):
    """Load the appropriate model based on gender selection"""
    global loaded_voice, current_gender, synthesize_args
    
    if gender not in MODEL_CONFIGS:
        raise ValueError(f"Invalid gender: {gender}. Must be 'male', 'female', 'finetuned-female', or 'finetune_girl_2'")
    
    # Check if we already have the correct model loaded
    if loaded_voice and current_gender == gender:
        _LOGGER.info(f"Model for {gender} already loaded")
        # Update synthesize_args with current parameters and return them
        synthesize_args = {
            "speaker_id": speaker,
            "length_scale": length_scale,
            "noise_scale": noise_scale,
            "noise_w": noise_w,
            "sentence_silence": sentence_silence,
        }
        return synthesize_args
    
    model_config = MODEL_CONFIGS[gender]
    model_path = Path(model_config["model"])
    config_path = Path(model_config["config"])
    
    download_dir = download_dir or data_dir
    
    # Check if model file exists
    if not model_path.exists():
        _LOGGER.warning(f"Model file not found: {model_path}")
        # Try to find/download the voice
        voices_info = get_voices(download_dir, update_voices=update_voices)
        ensure_voice_exists(model_path.name, [data_dir], download_dir, voices_info)
        model_path, config_path = find_voice(model_path.name, [data_dir])
    
    _LOGGER.info(f"Loading {gender} model from {model_path}")
    loaded_voice = PiperVoice.load(model_path, config_path=config_path, use_cuda=cuda)
    current_gender = gender
    
    synthesize_args = {
        "speaker_id": speaker,
        "length_scale": length_scale,
        "noise_scale": noise_scale,
        "noise_w": noise_w,
        "sentence_silence": sentence_silence,
    }
    _LOGGER.info(f"Successfully loaded {gender} model")
    return synthesize_args

@app.post("/tts")
def text_to_speech(
    text: str = Form(..., description="Text to synthesize"),
    gender: Gender = Form(..., description="Voice gender (male/female/finetuned-female/finetune_girl_2)"),
    speaker: Optional[int] = Form(0, description="Speaker ID"),
    length_scale: Optional[float] = Form(1.0, description="Length scale"),
    noise_scale: Optional[float] = Form(0.667, description="Noise scale"),
    noise_w: Optional[float] = Form(0.8, description="Noise weight"),
    sentence_silence: Optional[float] = Form(0.0, description="Sentence silence")
):
    """
    Single endpoint for Text-to-Speech with gender-based model selection.
    Automatically loads the appropriate model (male: chitvan, female: google, finetuned-female: finetuned girl, finetune_girl_2: finetuned girl 2) and synthesizes speech.
    """
    
    try:
        # Load the appropriate model based on gender
        synthesize_args = load_model_for_gender(
            gender=gender.value,
            cuda=True,  # Default to True for better performance
            data_dir=str(Path.cwd()),  # Use current working directory
            download_dir=None,  # Use default
            update_voices=False,  # Don't update voices by default
            speaker=speaker,
            length_scale=length_scale,
            noise_scale=noise_scale,
            noise_w=noise_w,
            sentence_silence=sentence_silence
        )
        
        if not loaded_voice:
            return JSONResponse(
                status_code=500, 
                content={"error": "Failed to load model"}
            )
        
        _LOGGER.info(f"Synthesizing text with {gender} voice: {text}")

        # Debug: Check config
        _LOGGER.debug(f"Voice config sample_rate: {loaded_voice.config.sample_rate}")
        _LOGGER.debug(f"Synthesize args: {synthesize_args}")

        # Synthesize speech
        wav_io = io.BytesIO()
        try:
            with wave.open(wav_io, "wb") as wav_file:
                _LOGGER.debug("Wave file opened successfully")
                loaded_voice.synthesize(text, wav_file, **synthesize_args)
                _LOGGER.debug("Synthesis completed successfully")
        except Exception as e:
            _LOGGER.error(f"Error during wave file operations: {str(e)}")
            raise
        wav_io.seek(0)
        
        return StreamingResponse(
            wav_io, 
            media_type="audio/wav", 
            headers={
                "Content-Disposition": f"attachment; filename=tts_{gender}_output.wav",
                "Content-Type": "audio/wav"
            }
        )
        
    except Exception as e:
        _LOGGER.error(f"Error in TTS synthesis: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"error": f"TTS synthesis failed: {str(e)}"}
        )



@app.get("/")
def root():
    return {
        "message": "Use POST /tts for text-to-speech with gender selection",
        "endpoint": "/tts",
        "genders": ["male", "female", "finetuned-female", "finetune_girl_2"],
        "models": {
            "male": "chitvan model",
            "female": "google model",
            "finetuned-female": "finetuned girl model",
            "finetune_girl_2": "finetuned girl 2 model"
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("src.python_run.piper.http_server:app", host="0.0.0.0", port=5000, reload=False)